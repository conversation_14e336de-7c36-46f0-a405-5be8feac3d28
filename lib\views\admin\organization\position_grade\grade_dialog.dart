import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/main.dart';

/// 职级创建弹窗
class GradeDialog extends StatefulWidget {
  final Widget? child;
  const GradeDialog({super.key, this.child});

  @override
  State<GradeDialog> createState() => GradeDialogState();
}

// 将 State 类改为公开，以便外部可以访问
class GradeDialogState extends State<GradeDialog> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;
  String? _basicValue;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();

  _getOptions() {
    return [
      SelectOption(value: 'changchun', label: '长春', group: '东北'),
      SelectOption(value: 'xian', label: '西安', group: '西北'),
      SelectOption(value: 'lanzhou', label: '兰州', group: '西北'),
      SelectOption(value: 'urumqi', label: '乌鲁木齐', group: '西北'),
      SelectOption(value: 'xining', label: '西宁', group: '西北'),
    ];
  }

  /// 重置数据
  void resetFormData() {}

  /// 提交
  createRequest(BuildContext context) {}

  /// 打开添加部门弹窗
  void showGradeDialog(BuildContext context) {
    double labelWidth = 80;

    /// 间距
    double spacing = 20;

    AppDialog.show(
      width: 480,
      context: context,
      title: '添加部门',
      isDrawer: true,
      slideDirection: SlideDirection.right,
      child: StatefulBuilder(
        builder: (context, setDialogState) {
          return Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppInput(
                  label: "名称",
                  labelWidth: labelWidth,
                  labelPosition: LabelPosition.left,
                  hintText: "名称",
                  size: InputSize.medium,
                  controller: _nameController,
                  maxLength: 30,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入名称';
                    }
                    return null;
                  },
                  onChanged: (value) {},
                ),
                SizedBox(height: spacing),
                Row(
                  children: [
                    Text('职级序列'),
                    Expanded(
                      child: AppSelect<String>(
                        navigatorKey: navigatorKey,
                        placeholder: '请选择String类型',
                        options: _getOptions(),
                        value: _basicValue,
                        onChanged: (value) {
                          setState(() {
                            _basicValue = value;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          StatefulBuilder(
            builder: (context, setState) {
              return Checkbox(
                value: isAddNext,
                onChanged: (value) {
                  setState(() {
                    isAddNext = !isAddNext;
                  });
                },
              );
            },
          ),
          Text('继续新建下一条'),
          const SizedBox(width: 10),
          AppButton(text: '取消', type: ButtonType.default_, onPressed: () => context.pop()),
          const SizedBox(width: 10),
          AppButton(text: '确定', type: ButtonType.primary, onPressed: () => createRequest(context)),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child ?? SizedBox();
  }
}
