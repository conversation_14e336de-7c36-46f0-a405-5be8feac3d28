import 'package:octasync_client/models/route_item.dart';
import 'package:octasync_client/views/admin/enterprise_management/enterprise_info/enterprise_info_page.dart';
import 'package:octasync_client/views/admin/organization/member_department/member_department_page.dart';
import 'package:octasync_client/views/admin/organization/position_grade/position_grade_page.dart';
import 'package:octasync_client/views/admin/project_management/project_config/project_config_page.dart';
import 'package:octasync_client/views/example/index.dart';
import 'package:octasync_client/views/login/login_page.dart';
import 'base_router_provider.dart';
import 'package:jyt_components_package/jyt_components_package.dart';
import 'package:octasync_client/views/collaboration/collaboration_page.dart';

/// Web端路由提供器
///
/// 实现Web端特定的路由配置，包括：
/// 1. Web端特定的路由列表，支持子菜单
/// 2. Web端特定的路由行为
///
/// 当应用在Web环境运行时，系统会使用这个路由提供者。
/// 它继承自 BaseRouterProvider，重写了 allRouteList 方法，提供Web端特定的路由配置。

class WebRouterProvider extends BaseRouterProvider {
  final List<RouteItem> _allRouteList = [
    RouteItem(
      path: '/collaboration',
      name: 'collaboration',
      title: '协同',
      icon: IconFont.mianxing_xietong,
      order: 5,
      builder: (context, state) => const CollaborationPage(),
    ),
    RouteItem(
      path: '/organization',
      name: 'organization',
      title: '组织架构',
      icon: IconFont.mianxing_xiaoxi,
      order: 10,
      redirect: (context, state) => null,
      routes: [
        RouteItem(
          path: 'memberDepartment',
          name: 'memberDepartment',
          title: '成员与部门',
          builder: (context, state) => const MemberDepartmentPage(),
        ),
        RouteItem(
          path: 'positionGrade',
          name: 'positionGrade',
          title: '职级职位管理',
          builder: (context, state) => const PositionGradePage(),
        ),
      ],
    ),
    RouteItem(
      path: '/enterpriseManagement',
      name: 'enterpriseManagement',
      title: '企业管理',
      icon: IconFont.mianxing_xiaoxi,
      order: 20,
      redirect: (context, state) => null,
      routes: [
        RouteItem(
          path: 'enterpriseInfo',
          name: 'enterpriseInfo',
          title: '企业信息',
          builder: (context, state) => const EnterpriseInfoPage(),
        ),
      ],
    ),
    RouteItem(
      path: '/projectManagement',
      name: 'projectManagement',
      title: '项目管理',
      icon: IconFont.mianxing_xiaoxi,
      order: 30,
      redirect: (context, state) => null,
      routes: [
        RouteItem(
          path: 'projectConfig',
          name: 'projectConfig',
          title: '项目配置',
          builder: (context, state) => const ProjectConfigPage(),
        ),
      ],
    ),
    RouteItem(
      path: '/login',
      name: 'login',
      title: '登录',
      icon: IconFont.mianxing_xiaoxi,
      showMenuLayout: true,
      order: 90,
      builder: (context, state) => const LoginPage(),
    ),
    //TODO:需要移除示例
    RouteItem(
      path: '/example',
      name: 'example',
      title: '示例',
      icon: IconFont.mianxing_renshihangzheng,
      order: 80,
      builder: (context, state) => const ExamplePage(),
    ),
  ];

  @override
  List<RouteItem> get allRouteList => _allRouteList;
}
